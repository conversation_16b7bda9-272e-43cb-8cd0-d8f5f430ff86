import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Configuration settings for the Anime Quote Bot"""
    
    # Telegram Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('ANIOTE_BOT_TOKEN')
    TELEGRAM_CHANNEL_ID = os.getenv('ANIOTE_TELEGRAM_CHANNEL_ID')
    
    # Gemini AI Configuration
    GEMINI_API_KEY = os.getenv('ANIOTE_GEMINI_KEY')
    
    # Database Configuration
    DATABASE_PATH = 'data/quotes.db'
    CSV_PATH = 'data/anime-quote.csv'
    
    # Posting Schedule Configuration
    POSTING_TIMES = [
        "08:00",  # Morning motivation
        "14:00",  # Afternoon inspiration
        "19:00"   # Evening reflection
    ]
    
    # Content Configuration
    MAX_QUOTE_LENGTH = 280  # Optimal for readability
    MIN_QUOTE_LENGTH = 20   # Minimum meaningful length
    
    # Hashtag Configuration
    DEFAULT_HASHTAGS = ["#AnimeQuotes", "#AnimeWisdom", "#Motivation"]
    MAX_HASHTAGS_PER_POST = 8
    
    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/aniote.log"
    
    @classmethod
    def validate(cls):
        """Validate that all required settings are present"""
        required_settings = [
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHANNEL_ID', 
            'GEMINI_API_KEY'
        ]
        
        missing = []
        for setting in required_settings:
            if not getattr(cls, setting):
                missing.append(setting)
        
        if missing:
            raise ValueError(f"Missing required settings: {', '.join(missing)}")
        
        return True

# Create settings instance
settings = Settings()
