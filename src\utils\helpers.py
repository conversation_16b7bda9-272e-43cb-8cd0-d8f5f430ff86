import logging
import colorlog
import os
from datetime import datetime
from config.settings import settings

def setup_logging():
    """Setup logging configuration with colors and file output"""
    
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
    
    # Create formatter for console output with colors
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # Create formatter for file output
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Setup root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler(settings.LOG_FILE)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    return logger

def format_quote_post(quote_data, hashtags=None):
    """Format a quote for posting to Telegram channel"""
    
    quote = quote_data['Quote'].strip()
    character = quote_data['Character']
    anime = quote_data['Anime']
    
    # Clean up quote formatting
    if quote.startswith('"') and quote.endswith('"'):
        quote = quote[1:-1]
    
    # Create the post content
    post_content = f'💫 "{quote}"\n\n'
    post_content += f'— **{character}** from *{anime}*\n\n'
    
    # Add hashtags
    if hashtags:
        hashtag_line = ' '.join(hashtags)
        post_content += hashtag_line
    
    return post_content

def get_current_time_slot():
    """Get the current time slot for posting"""
    current_hour = datetime.now().hour
    
    if 6 <= current_hour < 12:
        return "morning"
    elif 12 <= current_hour < 18:
        return "afternoon"
    elif 18 <= current_hour < 24:
        return "evening"
    else:
        return "night"

def clean_text(text):
    """Clean text for better formatting"""
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    # Fix common formatting issues
    text = text.replace('""', '"')
    text = text.replace("''", "'")
    
    return text.strip()

def truncate_text(text, max_length=280):
    """Truncate text to specified length while preserving words"""
    if len(text) <= max_length:
        return text
    
    # Find the last space before max_length
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > 0:
        truncated = truncated[:last_space]
    
    return truncated + "..."
