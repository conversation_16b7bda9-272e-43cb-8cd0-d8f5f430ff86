#!/usr/bin/env python3
"""
Setup script for configuring the Aniote bot.
Helps with initial configuration and testing.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.telegram_bot import TelegramBot, test_bot_sync
from src.data.quote_manager import QuoteManager
from src.utils.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def check_environment():
    """Check if environment is properly configured."""
    print("🔍 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        print("📝 Please copy .env.example to .env and fill in your API keys")
        return False
    
    print("✅ .env file found")
    
    # Check required configuration
    missing_config = []
    
    if not config.TELEGRAM_BOT_TOKEN:
        missing_config.append("TELEGRAM_BOT_TOKEN")
    
    if not config.TELEGRAM_CHANNEL_ID:
        missing_config.append("TELEGRAM_CHANNEL_ID")
    
    if not config.GEMINI_API_KEY:
        missing_config.append("GEMINI_API_KEY")
    
    if missing_config:
        print(f"❌ Missing configuration: {', '.join(missing_config)}")
        return False
    
    print("✅ All required configuration found")
    return True


def check_dataset():
    """Check if the anime quotes dataset is accessible."""
    print("\n📊 Checking anime quotes dataset...")
    
    try:
        quote_manager = QuoteManager()
        stats = quote_manager.get_stats()
        
        if stats.get('total_quotes', 0) == 0:
            print("❌ No quotes found in dataset")
            return False
        
        print(f"✅ Dataset loaded successfully")
        print(f"   📈 {stats.get('total_quotes', 0)} total quotes")
        print(f"   🎬 {stats.get('unique_anime', 0)} unique anime")
        print(f"   👥 {stats.get('unique_characters', 0)} unique characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load dataset: {e}")
        return False


def test_telegram_bot():
    """Test Telegram bot connection."""
    print("\n🤖 Testing Telegram bot connection...")
    
    try:
        success = test_bot_sync()
        
        if success:
            print("✅ Telegram bot connection successful")
            return True
        else:
            print("❌ Telegram bot connection failed")
            print("💡 Check your bot token and channel ID")
            return False
            
    except Exception as e:
        print(f"❌ Bot test failed: {e}")
        return False


def test_content_generation():
    """Test content generation."""
    print("\n📝 Testing content generation...")
    
    try:
        quote_manager = QuoteManager()
        content = quote_manager.get_daily_quote_content()
        
        if content:
            print("✅ Content generation successful")
            print(f"   📋 Generated content for quote ID {content.quote.id}")
            print(f"   🎭 Character: {content.quote.character}")
            print(f"   📺 Anime: {content.quote.anime}")
            print(f"   🏷️ Themes: {', '.join(content.themes)}")
            
            # Don't mark as posted since this is just a test
            if content.quote.id in quote_manager.data_processor.posted_quotes:
                quote_manager.data_processor.posted_quotes.remove(content.quote.id)
            
            return True
        else:
            print("❌ Content generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Content generation test failed: {e}")
        return False


def show_usage_examples():
    """Show usage examples."""
    print("\n📚 Usage Examples:")
    print("="*50)
    print("# Post a daily quote:")
    print("python scripts/post_daily_quote.py")
    print()
    print("# Preview content without posting:")
    print("python scripts/preview_content.py")
    print()
    print("# Post with specific theme:")
    print("python scripts/post_daily_quote.py --theme motivation")
    print()
    print("# Post from specific anime:")
    print("python scripts/post_daily_quote.py --anime 'Naruto'")
    print()
    print("# Preview multiple quotes:")
    print("python scripts/preview_content.py --count 5")
    print()
    print("# Show database statistics:")
    print("python scripts/preview_content.py --stats")
    print()
    print("# Test bot connection:")
    print("python scripts/post_daily_quote.py --test-bot")


def main():
    """Main setup function."""
    print("🚀 Aniote Bot Setup & Configuration Check")
    print("="*50)
    
    all_checks_passed = True
    
    # Run all checks
    if not check_environment():
        all_checks_passed = False
    
    if not check_dataset():
        all_checks_passed = False
    
    if not test_telegram_bot():
        all_checks_passed = False
    
    if not test_content_generation():
        all_checks_passed = False
    
    print("\n" + "="*50)
    
    if all_checks_passed:
        print("🎉 All checks passed! Your bot is ready to use.")
        show_usage_examples()
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        print("\n💡 Common solutions:")
        print("   • Make sure .env file exists with correct API keys")
        print("   • Verify your Telegram bot token is correct")
        print("   • Check that your channel ID is correct (use @username or chat ID)")
        print("   • Ensure the bot is added to your channel as an admin")
        print("   • Verify your Gemini API key is valid")
    
    return 0 if all_checks_passed else 1


if __name__ == "__main__":
    exit(main())
