#!/usr/bin/env python3
"""
Manual script for posting daily anime quotes to Telegram channel.
Run this script whenever you want to post a new quote.
"""

import sys
import os
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.quote_manager import QuoteManager
from src.core.telegram_bot import TelegramBot, post_quote_sync
from src.utils.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """Main function for posting daily quotes."""
    parser = argparse.ArgumentParser(description="Post daily anime quote to Telegram channel")
    parser.add_argument("--theme", type=str, help="Force a specific theme for the quote")
    parser.add_argument("--quote-id", type=int, help="Post a specific quote by ID")
    parser.add_argument("--anime", type=str, help="Post a quote from specific anime")
    parser.add_argument("--character", type=str, help="Post a quote from specific character")
    parser.add_argument("--themed-day", action="store_true", help="Use current day's theme")
    parser.add_argument("--preview-only", action="store_true", help="Only preview, don't post")
    parser.add_argument("--test-bot", action="store_true", help="Test bot connection")
    
    args = parser.parse_args()
    
    # Validate configuration
    if not config.validate():
        logger.error("Configuration validation failed. Please check your .env file.")
        return 1
    
    # Test bot connection if requested
    if args.test_bot:
        logger.info("Testing bot connection...")
        bot = TelegramBot()
        if bot.test_connection():
            logger.info("✅ Bot connection successful!")
            return 0
        else:
            logger.error("❌ Bot connection failed!")
            return 1
    
    try:
        # Initialize quote manager
        quote_manager = QuoteManager()
        
        # Generate content based on arguments
        content = None
        
        if args.quote_id:
            logger.info(f"Generating content for quote ID: {args.quote_id}")
            content = quote_manager.get_quote_by_id(args.quote_id)
            
        elif args.anime:
            logger.info(f"Generating content for anime: {args.anime}")
            content = quote_manager.get_anime_specific_content(args.anime)
            
        elif args.character:
            logger.info(f"Generating content for character: {args.character}")
            content = quote_manager.get_character_spotlight_content(args.character)
            
        elif args.themed_day:
            logger.info("Generating themed content for current day")
            content = quote_manager.get_daily_themed_content()
            
        else:
            logger.info("Generating daily quote content")
            content = quote_manager.get_daily_quote_content(force_theme=args.theme)
        
        if not content:
            logger.error("Failed to generate content")
            return 1
        
        # Show preview
        print("\n" + "="*60)
        print("CONTENT PREVIEW")
        print("="*60)
        print(quote_manager.preview_content(content))
        
        # If preview only, exit here
        if args.preview_only:
            logger.info("Preview mode - not posting to channel")
            return 0
        
        # Confirm posting
        if not config.is_development():
            confirm = input("\nPost this content to Telegram channel? (y/N): ")
            if confirm.lower() != 'y':
                logger.info("Posting cancelled by user")
                return 0
        
        # Post to Telegram
        logger.info("Posting to Telegram channel...")
        success = post_quote_sync(content)
        
        if success:
            logger.info("✅ Quote posted successfully!")
            print(f"\n🎉 Successfully posted quote ID {content.quote.id} to your Telegram channel!")
            return 0
        else:
            logger.error("❌ Failed to post quote")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
