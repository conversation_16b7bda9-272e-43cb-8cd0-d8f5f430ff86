["src/tests/test_basic_functionality.py::TestConfiguration::test_config_boolean_parsing", "src/tests/test_basic_functionality.py::TestConfiguration::test_config_default_values", "src/tests/test_basic_functionality.py::TestConfiguration::test_config_has_required_attributes", "src/tests/test_basic_functionality.py::TestConfiguration::test_config_integer_parsing", "src/tests/test_basic_functionality.py::TestDataProcessor::test_quote_creation", "src/tests/test_basic_functionality.py::TestDataProcessor::test_quote_to_dict", "src/tests/test_basic_functionality.py::TestThemeDetector::test_daily_theme_mapping", "src/tests/test_basic_functionality.py::TestThemeDetector::test_empty_quote_handling", "src/tests/test_basic_functionality.py::TestThemeDetector::test_friendship_theme_detection", "src/tests/test_basic_functionality.py::TestThemeDetector::test_get_theme_emoji", "src/tests/test_basic_functionality.py::TestThemeDetector::test_get_theme_hashtags", "src/tests/test_basic_functionality.py::TestThemeDetector::test_love_theme_detection", "src/tests/test_basic_functionality.py::TestThemeDetector::test_motivation_theme_detection", "src/tests/test_basic_functionality.py::TestThemeDetector::test_theme_detector_initialization"]