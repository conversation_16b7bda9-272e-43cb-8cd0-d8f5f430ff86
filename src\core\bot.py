import logging
import asyncio
from telegram import Bo<PERSON>
from telegram.error import Telegram<PERSON>rror
from config.settings import settings
from src.data.quote_manager import QuoteManager
from src.utils.gemini_client import GeminiClient
from src.utils.helpers import format_quote_post

logger = logging.getLogger(__name__)

class AnimeQuoteBot:
    """Main bot class for the Anime Quote Telegram Channel"""
    
    def __init__(self):
        self.token = settings.TELEGRAM_BOT_TOKEN
        self.channel_id = settings.TELEGRAM_CHANNEL_ID
        self.bot = None
        self.quote_manager = None
        self.gemini_client = None
        self._validate_settings()
    
    def _validate_settings(self):
        """Validate bot settings"""
        try:
            settings.validate()
            logger.info("Bot settings validated successfully")
        except ValueError as e:
            logger.error(f"Invalid settings: {e}")
            raise
    
    async def initialize(self):
        """Initialize the bot and its components"""
        try:
            # Initialize Telegram bot
            self.bot = Bot(token=self.token)

            # Test bot connection
            bot_info = await self.bot.get_me()
            logger.info(f"Bot initialized: @{bot_info.username}")

            # Initialize quote manager and AI client
            self.quote_manager = QuoteManager()
            self.gemini_client = GeminiClient()
            logger.info("Bot components initialized successfully")

            return True

        except Exception as e:
            logger.error(f"Error initializing bot: {e}")
            return False
    
    async def post_to_channel(self, content, parse_mode='Markdown'):
        """Post content to the Telegram channel"""
        try:
            if not self.bot:
                logger.error("Bot not initialized")
                return False
            
            message = await self.bot.send_message(
                chat_id=self.channel_id,
                text=content,
                parse_mode=parse_mode
            )
            
            logger.info(f"Message posted successfully: {message.message_id}")
            return True
            
        except TelegramError as e:
            logger.error(f"Telegram error posting message: {e}")
            return False
        except Exception as e:
            logger.error(f"Error posting to channel: {e}")
            return False
    
    async def test_connection(self):
        """Test the bot connection and channel access"""
        try:
            if not self.bot:
                await self.initialize()
            
            # Test bot info
            bot_info = await self.bot.get_me()
            logger.info(f"Bot connection test successful: @{bot_info.username}")
            
            # Test channel access by getting chat info
            try:
                chat_info = await self.bot.get_chat(self.channel_id)
                logger.info(f"Channel access test successful: {chat_info.title}")
            except TelegramError as e:
                logger.warning(f"Channel access test failed: {e}")
                logger.info("This might be normal if the bot hasn't been added to the channel yet")
            
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    async def post_test_message(self):
        """Post a test message to verify everything works"""
        test_content = """🤖 **Anime Quote Bot Test**

"The only one who can decide your worth is you."

— **Jabami Yumeko** from *Kakegurui*

#AnimeQuotes #TestPost #BotOnline"""
        
        success = await self.post_to_channel(test_content)
        if success:
            logger.info("Test message posted successfully")
        else:
            logger.error("Failed to post test message")
        
        return success
    
    async def post_random_quote(self, category=None):
        """Post a random quote immediately"""
        try:
            logger.info(f"Posting random quote (category: {category})")

            if category:
                quote_data = self.quote_manager.get_quote_by_category(category)
            else:
                quote_data = self.quote_manager.get_random_quote()

            if not quote_data:
                logger.error("No quote data available")
                return False

            # Enhance with AI
            enhancement = self.gemini_client.enhance_post_content(quote_data, 'manual')

            # Format post
            post_content = format_quote_post(quote_data, enhancement['hashtags'])

            # Post to channel
            success = await self.post_to_channel(post_content)

            if success:
                self.quote_manager.mark_as_posted(quote_data['ID'], 'manual')
                logger.info(f"Successfully posted quote: {quote_data['ID']}")
                return True
            else:
                logger.error("Failed to post quote")
                return False

        except Exception as e:
            logger.error(f"Error posting random quote: {e}")
            return False

    async def post_character_spotlight(self):
        """Post a character spotlight"""
        try:
            logger.info("Creating character spotlight post")

            # Get popular characters
            popular_characters = self.quote_manager.get_popular_characters(20)

            if not popular_characters:
                logger.error("No character data available for spotlight")
                return False

            # Select a random popular character
            import random
            character_name = random.choice(list(popular_characters.keys()))

            # Get quotes from this character
            character_quotes = self.quote_manager.quotes_df[
                self.quote_manager.quotes_df['Character'] == character_name
            ]

            if character_quotes.empty:
                logger.error(f"No quotes found for character: {character_name}")
                return False

            # Get anime name
            anime_name = character_quotes.iloc[0]['Anime']
            quotes_list = character_quotes.head(3).to_dict('records')

            # Generate spotlight content
            spotlight_text = self.gemini_client.generate_character_spotlight(
                character_name, anime_name, quotes_list
            )

            # Format spotlight post
            post_content = f"🌟 **Character Spotlight** 🌟\n\n"
            post_content += f"**{character_name}** from *{anime_name}*\n\n"

            if spotlight_text:
                post_content += f"{spotlight_text}\n\n"

            # Add a featured quote
            featured_quote = quotes_list[0]
            post_content += f'💫 "{featured_quote["Quote"]}"\n\n'

            # Add hashtags
            hashtags = [
                "#CharacterSpotlight",
                f"#{anime_name.replace(' ', '').replace(':', '')}",
                f"#{character_name.replace(' ', '')}",
                "#AnimeCharacters",
                "#AnimeQuotes"
            ]
            post_content += ' '.join(hashtags)

            # Post to channel
            success = await self.post_to_channel(post_content)

            if success:
                self.quote_manager.mark_as_posted(featured_quote['ID'], 'spotlight')
                logger.info(f"Successfully posted character spotlight: {character_name}")
                return True
            else:
                logger.error("Failed to post character spotlight")
                return False

        except Exception as e:
            logger.error(f"Error creating character spotlight: {e}")
            return False
    
    async def start(self):
        """Initialize the bot for manual posting"""
        try:
            logger.info("Initializing Anime Quote Bot for manual posting")

            # Initialize bot
            if not await self.initialize():
                logger.error("Failed to initialize bot")
                return False

            # Test connection
            if not await self.test_connection():
                logger.error("Connection test failed")
                return False

            logger.info("Bot initialized successfully and ready for manual posting")
            return True

        except Exception as e:
            logger.error(f"Error initializing bot: {e}")
            return False
    
    async def shutdown(self):
        """Gracefully shutdown the bot"""
        logger.info("Shutting down bot")
        logger.info("Bot shutdown complete")
