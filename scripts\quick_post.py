#!/usr/bin/env python3
"""
Quick posting script for Anime Quote Bot
Use this for fast posting without the interactive menu
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.bot import AnimeQuoteBot
from src.utils.helpers import setup_logging

async def quick_post(post_type="random", category=None):
    """Quick post function"""
    
    setup_logging()
    
    print(f"🚀 Quick posting: {post_type}")
    if category:
        print(f"📂 Category: {category}")
    
    try:
        bot = AnimeQuoteBot()
        
        # Initialize bot
        if not await bot.start():
            print("❌ Failed to initialize bot")
            return False
        
        success = False
        
        if post_type == "random":
            success = await bot.post_random_quote(category)
        elif post_type == "spotlight":
            success = await bot.post_character_spotlight()
        elif post_type == "test":
            success = await bot.post_test_message()
        
        if success:
            print("✅ Posted successfully!")
        else:
            print("❌ Failed to post")
        
        await bot.shutdown()
        return success
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Parse command line arguments
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python scripts/quick_post.py random [category]")
        print("  python scripts/quick_post.py spotlight")
        print("  python scripts/quick_post.py test")
        print("")
        print("Categories: motivation, philosophy, wisdom, love, friendship")
        sys.exit(1)
    
    post_type = sys.argv[1]
    category = sys.argv[2] if len(sys.argv) > 2 else None
    
    asyncio.run(quick_post(post_type, category))
