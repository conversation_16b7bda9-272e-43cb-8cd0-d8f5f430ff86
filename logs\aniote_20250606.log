2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:25:59 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:27:22 - src.data.data_processor - INFO - Loaded 8334 quotes from C:\Users\<USER>\Documents\aniote\data\anime-quote.csv
2025-06-06 01:27:22 - src.data.data_processor - INFO - Data cleaning complete: 8334 -> 8318 quotes
2025-06-06 01:27:22 - src.data.data_processor - INFO - No previous posting history found
2025-06-06 01:27:22 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:27:22 - src.core.gemini_client - ERROR - Failed to initialize Gemini client: Gemini API key not found in configuration
2025-06-06 01:27:22 - src.core.telegram_bot - ERROR - Failed to initialize Telegram bot: Telegram bot token not found in configuration
2025-06-06 01:27:22 - src.core.telegram_bot - ERROR - Error in synchronous bot testing: Telegram bot token not found in configuration
2025-06-06 01:27:22 - src.data.data_processor - INFO - Loaded 8334 quotes from C:\Users\<USER>\Documents\aniote\data\anime-quote.csv
2025-06-06 01:27:22 - src.data.data_processor - INFO - Data cleaning complete: 8334 -> 8318 quotes
2025-06-06 01:27:22 - src.data.data_processor - INFO - No previous posting history found
2025-06-06 01:27:22 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:27:22 - src.core.gemini_client - ERROR - Failed to initialize Gemini client: Gemini API key not found in configuration
2025-06-06 01:28:15 - src.data.data_processor - INFO - Loaded 8334 quotes from C:\Users\<USER>\Documents\aniote\data\anime-quote.csv
2025-06-06 01:28:15 - src.data.data_processor - INFO - Data cleaning complete: 8334 -> 8318 quotes
2025-06-06 01:28:15 - src.data.data_processor - INFO - No previous posting history found
2025-06-06 01:28:15 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:15 - src.core.content_generator - INFO - Content generator initialized
2025-06-06 01:28:15 - src.core.content_generator - INFO - AI analysis disabled
2025-06-06 01:28:15 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:15 - src.data.quote_manager - INFO - Quote manager initialized
2025-06-06 01:28:24 - src.data.data_processor - INFO - Loaded 8334 quotes from C:\Users\<USER>\Documents\aniote\data\anime-quote.csv
2025-06-06 01:28:24 - src.data.data_processor - INFO - Data cleaning complete: 8334 -> 8318 quotes
2025-06-06 01:28:24 - src.data.data_processor - INFO - No previous posting history found
2025-06-06 01:28:24 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:24 - src.core.content_generator - INFO - Content generator initialized
2025-06-06 01:28:24 - src.core.content_generator - INFO - AI analysis disabled
2025-06-06 01:28:24 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:24 - src.data.quote_manager - INFO - Quote manager initialized
2025-06-06 01:28:24 - src.data.data_processor - INFO - Selected random quote ID 7344 from Tengen Toppa Gurren Lagann
2025-06-06 01:28:24 - src.core.content_generator - INFO - Generated daily quote content for ID 7344
2025-06-06 01:28:24 - src.data.quote_manager - INFO - Generated daily quote content for quote ID 7344
2025-06-06 01:28:24 - src.data.data_processor - INFO - Selected random quote ID 2665 from Ghost In The Shell
2025-06-06 01:28:24 - src.core.content_generator - INFO - Generated daily quote content for ID 2665
2025-06-06 01:28:24 - src.data.quote_manager - INFO - Generated daily quote content for quote ID 2665
2025-06-06 01:28:24 - src.data.data_processor - INFO - Selected random quote ID 4246 from Hyouka
2025-06-06 01:28:24 - src.core.content_generator - INFO - Generated daily quote content for ID 4246
2025-06-06 01:28:24 - src.data.quote_manager - INFO - Generated daily quote content for quote ID 4246
2025-06-06 01:28:37 - src.data.data_processor - INFO - Loaded 8334 quotes from C:\Users\<USER>\Documents\aniote\data\anime-quote.csv
2025-06-06 01:28:37 - src.data.data_processor - INFO - Data cleaning complete: 8334 -> 8318 quotes
2025-06-06 01:28:37 - src.data.data_processor - INFO - Loaded 1 previously posted quotes
2025-06-06 01:28:37 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:37 - src.core.content_generator - INFO - Content generator initialized
2025-06-06 01:28:37 - src.core.content_generator - INFO - AI analysis disabled
2025-06-06 01:28:37 - src.utils.theme_detector - INFO - Initialized theme detector with 10 themes
2025-06-06 01:28:37 - src.data.quote_manager - INFO - Quote manager initialized
2025-06-06 01:28:37 - src.data.data_processor - INFO - Selected random quote ID 1465 from Gintama
2025-06-06 01:28:37 - src.core.content_generator - INFO - Generated daily quote content for ID 1465
2025-06-06 01:28:37 - src.data.quote_manager - INFO - Generated daily quote content for quote ID 1465
