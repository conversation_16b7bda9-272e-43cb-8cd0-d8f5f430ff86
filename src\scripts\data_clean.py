import pandas as pd
import re
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from time import time

# Optional: Import fuzzywuzzy for near-duplicate detection
try:
    from fuzzywuzzy import fuzz
    FUZZY_AVAILABLE = True
except ImportError:
    FUZZY_AVAILABLE = False
    print("Fuzzywuzzy not installed. Skipping near-duplicate detection. Install with: pip install fuzzywuzzy python-Levenshtein")

# Load the dataset with semicolon delimiter
try:
    df = pd.read_csv('lessreal-data.csv', sep=';', usecols=['ID', 'Anime', 'Character', 'Quote'])
except FileNotFoundError:
    print("Error: 'lessreal-data.csv' not found. Please ensure the file is in the script's directory.")
    sys.exit(1)

# 1. Remove rows with missing or invalid data
df = df.dropna(subset=['Anime', 'Character', 'Quote'], how='all')

# 2. Handle missing or non-numeric IDs
df['ID'] = pd.to_numeric(df['ID'], errors='coerce')
df = df.dropna(subset=['ID'])
df['ID'] = df['ID'].astype(int)
df['ID'] = range(len(df))  # Reassign sequential IDs

# 3. Clean Anime names: Remove parentheses and standardize capitalization
df['Anime'] = df['Anime'].str.replace(r'[\(\)]', '', regex=True).str.strip().str.title()

# 4. Clean Character names: Strip whitespace and standardize capitalization
df['Character'] = df['Character'].str.strip().str.title()

# 5. Clean Quote column
def clean_quote(text):
    if pd.isna(text):
        return text
    # Remove extra quotation marks
    text = re.sub(r'"{2,}', '"', text)
    # Replace multiple newlines, tabs, or special characters with a single space
    text = re.sub(r'[\n\t]+', ' ', text)
    # Remove excessive punctuation (e.g., multiple !, ?, .)
    text = re.sub(r'[!?.]{2,}', lambda x: x.group(0)[0], text)
    # Remove control characters
    text = re.sub(r'[\x00-\x1F\x7F]', '', text)
    # Strip leading/trailing whitespace
    return text.strip()

df['Quote'] = df['Quote'].apply(clean_quote)

# 6. Drop rows with missing Character or Quote
df = df.dropna(subset=['Character', 'Quote'])
print("=== Missing Values After Cleaning ===")
print(df.isnull().sum())

# 7. Detect near-duplicate quotes (similarity >= 90%) on a sample
def find_near_duplicates(df, threshold=90, max_comparisons=10000):
    duplicates = []
    comparisons_done = 0
    start_time = time()
    sample_df = df.sample(n=min(1000, len(df)), random_state=42)  # Limit to 1000 rows for performance
    sample_df = sample_df.reset_index(drop=True)
    total_comparisons = (len(sample_df) * (len(sample_df) - 1)) // 2
    print(f"\nStarting near-duplicate detection on {len(sample_df)} quotes ({total_comparisons} comparisons)...")
    
    for i in range(len(sample_df)):
        for j in range(i + 1, len(sample_df)):
            if comparisons_done >= max_comparisons:
                print(f"Reached max comparisons ({max_comparisons}). Stopping early.")
                break
            if pd.notna(sample_df['Quote'].iloc[i]) and pd.notna(sample_df['Quote'].iloc[j]):
                similarity = fuzz.ratio(sample_df['Quote'].iloc[i], sample_df['Quote'].iloc[j])
                if similarity >= threshold:
                    duplicates.append((sample_df['ID'].iloc[i], sample_df['ID'].iloc[j], similarity,
                                      sample_df['Anime'].iloc[i], sample_df['Character'].iloc[i],
                                      sample_df['Quote'].iloc[i],
                                      sample_df['Anime'].iloc[j], sample_df['Character'].iloc[j],
                                      sample_df['Quote'].iloc[j]))
            comparisons_done += 1
            if comparisons_done % 1000 == 0:
                elapsed = time() - start_time
                print(f"Processed {comparisons_done}/{total_comparisons} comparisons ({elapsed:.1f}s)")
        if comparisons_done >= max_comparisons:
            break
    
    print(f"Near-duplicate detection completed in {time() - start_time:.1f} seconds.")
    return duplicates

# Run near-duplicate detection if fuzzywuzzy is available
if FUZZY_AVAILABLE:
    try:
        near_duplicates = find_near_duplicates(df)
        if near_duplicates:
            print("\n=== Near-Duplicate Quotes ===")
            for id1, id2, sim, anime1, char1, quote1, anime2, char2, quote2 in near_duplicates:
                print(f"IDs {id1} and {id2} (Similarity: {sim}%):")
                print(f"  {anime1} - {char1}: {quote1}")
                print(f"  {anime2} - {char2}: {quote2}")
        else:
            print("\nNo near-duplicate quotes found (similarity >= 90%) in sample.")
    except Exception as e:
        print(f"\nError during near-duplicate detection: {e}. Skipping.")
else:
    print("\nSkipping near-duplicate detection due to missing fuzzywuzzy.")

# 8. Remove exact duplicates
df = df.drop_duplicates(subset=['Anime', 'Character', 'Quote'], keep='first')

# 9. Validate quotes: Flag quotes shorter than 5 characters
df['Quote_Length'] = df['Quote'].str.len()
short_quotes = df[df['Quote_Length'] < 5]
if not short_quotes.empty:
    print("\n=== Short Quotes Found ===")
    print(short_quotes[['ID', 'Anime', 'Character', 'Quote']])
else:
    print("\nNo quotes shorter than 5 characters found.")

# 10. Reset index
df = df.reset_index(drop=True)

# 11. Save the cleaned dataset
output_file = 'lessreal-data-cleaned-final.csv'
df.to_csv(output_file, index=False, sep=',')
print(f"\n=== Cleaned dataset saved to '{output_file}' ===")

# Save2. Display basic information
print("\n=== Cleaned Dataset Info ===")
print(df.info())

# 3. Print first few rows
print("\n=== First 5 Rows of Cleaned Dataset ===")
print(df.head(5))

# 13. Basic statistics
print("\n=== Basic Statistics ===")

print(f"Total rows: {len(df)}")
print(f"Unique anime: {df['Anime'].nunique()}")
print(f"Unique characters: {df['Character'].nunique()}")
print(f"Average quote length: {df['Quote_Length'].mean():.2f} characters")
print(f"Minimum quote length: {df['Quote_Length'].min()} characters")
print(f"Maximum quote length: {df['Quote_Length'].max()} characters")

# 14. Visualize quote length distribution
plt.figure(figsize=(8, 5))
sns.histplot(df['Quote_Length'], bins=30, kde=True, color='teal')
plt.title('Distribution of Quote Lengths')
plt.xlabel('Quote Length (Characters)')
plt.ylabel('Count')
plt.show()

# 15. Visualize top 10 anime by quote count
plt.figure(figsize=(10, 6))
top_anime = df['Anime'].value_counts().head(10)
sns.barplot(x=top_anime.values, y=top_anime.index, hue=top_anime.index, palette='viridis')
plt.title('Top 10 Anime by Quote Count')
plt.xlabel('Number of Quotes')
plt.ylabel('Anime')
plt.show()

# 16. Visualize top 10 characters by quote count
plt.figure(figsize=(10, 6))
top_characters = df['Character'].value_counts().head(10)
sns.barplot(x=top_characters.values, y=top_characters.index, hue=top_characters.index, palette='magma')
plt.title('Top 10 Characters by Quote Count')
plt.xlabel('Number of Quotes')
plt.ylabel('Character')
plt.show()