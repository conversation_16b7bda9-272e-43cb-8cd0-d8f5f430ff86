#!/usr/bin/env python3
"""
Script for previewing content without posting.
Useful for testing content generation and themes.
"""

import sys
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.quote_manager import QuoteManager
from src.utils.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """Main function for previewing content."""
    parser = argparse.ArgumentParser(description="Preview anime quote content")
    parser.add_argument("--theme", type=str, help="Force a specific theme")
    parser.add_argument("--quote-id", type=int, help="Preview specific quote by ID")
    parser.add_argument("--anime", type=str, help="Preview quote from specific anime")
    parser.add_argument("--character", type=str, help="Preview quote from specific character")
    parser.add_argument("--themed-day", action="store_true", help="Use current day's theme")
    parser.add_argument("--count", type=int, default=1, help="Number of previews to generate")
    parser.add_argument("--stats", action="store_true", help="Show database statistics")
    
    args = parser.parse_args()
    
    try:
        # Initialize quote manager
        quote_manager = QuoteManager()
        
        # Show stats if requested
        if args.stats:
            stats = quote_manager.get_stats()
            print("\n" + "="*60)
            print("DATABASE STATISTICS")
            print("="*60)
            print(f"Total quotes: {stats.get('total_quotes', 0)}")
            print(f"Unique anime: {stats.get('unique_anime', 0)}")
            print(f"Unique characters: {stats.get('unique_characters', 0)}")
            print(f"Posted quotes: {stats.get('posted_quotes', 0)}")
            print(f"Available quotes: {stats.get('available_quotes', 0)}")
            print(f"Average quote length: {stats.get('avg_quote_length', 0):.1f} chars")
            print(f"Available themes: {stats.get('available_themes', 0)}")
            print(f"Theme names: {', '.join(stats.get('theme_names', []))}")
            print("="*60)
            return 0
        
        # Generate previews
        for i in range(args.count):
            if args.count > 1:
                print(f"\n{'='*20} PREVIEW {i+1}/{args.count} {'='*20}")
            
            # Generate content based on arguments
            content = None
            
            if args.quote_id:
                content = quote_manager.get_quote_by_id(args.quote_id)
                
            elif args.anime:
                content = quote_manager.get_anime_specific_content(args.anime)
                
            elif args.character:
                content = quote_manager.get_character_spotlight_content(args.character)
                
            elif args.themed_day:
                content = quote_manager.get_daily_themed_content()
                
            else:
                content = quote_manager.get_daily_quote_content(force_theme=args.theme)
            
            if content:
                print(quote_manager.preview_content(content))
            else:
                print("❌ Failed to generate content")
                
            # Don't mark as posted since this is just a preview
            if content and hasattr(content.quote, 'id'):
                # Remove from posted list since this was just a preview
                if content.quote.id in quote_manager.data_processor.posted_quotes:
                    quote_manager.data_processor.posted_quotes.remove(content.quote.id)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Preview cancelled by user")
        return 0
    except Exception as e:
        logger.error(f"Error during preview: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
