import schedule
import asyncio
import logging
from datetime import datetime, time
from config.settings import settings
from src.data.quote_manager import Quote<PERSON>anager
from src.utils.gemini_client import Gemini<PERSON>lient
from src.utils.helpers import format_quote_post, get_current_time_slot

logger = logging.getLogger(__name__)

class PostScheduler:
    """Handles scheduling and automated posting of quotes"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.quote_manager = QuoteManager()
        self.gemini_client = GeminiClient()
        self.is_running = False
        self._setup_schedule()
    
    def _setup_schedule(self):
        """Setup the posting schedule"""
        try:
            for post_time in settings.POSTING_TIMES:
                schedule.every().day.at(post_time).do(self._scheduled_post_wrapper)
                logger.info(f"Scheduled daily post at {post_time}")
            
            # Weekly character spotlight (Sundays at 10:00)
            schedule.every().sunday.at("10:00").do(self._character_spotlight_wrapper)
            logger.info("Scheduled weekly character spotlight")
            
            logger.info("Post schedule configured successfully")
            
        except Exception as e:
            logger.error(f"Error setting up schedule: {e}")
            raise
    
    def _scheduled_post_wrapper(self):
        """Wrapper for scheduled posts to handle async"""
        asyncio.create_task(self._create_scheduled_post())
    
    def _character_spotlight_wrapper(self):
        """Wrapper for character spotlight posts"""
        asyncio.create_task(self._create_character_spotlight())
    
    async def _create_scheduled_post(self):
        """Create and post a scheduled quote"""
        try:
            logger.info("Creating scheduled post")
            
            # Get time-appropriate quote category
            time_slot = get_current_time_slot()
            category_map = {
                'morning': 'motivation',
                'afternoon': 'wisdom',
                'evening': 'philosophy',
                'night': 'life_lessons'
            }
            
            category = category_map.get(time_slot, 'wisdom')
            quote_data = self.quote_manager.get_quote_by_category(category)
            
            if not quote_data:
                logger.error("No quote data available for scheduled post")
                return
            
            # Enhance with AI
            enhancement = self.gemini_client.enhance_post_content(quote_data, 'daily')
            
            # Format post
            post_content = format_quote_post(quote_data, enhancement['hashtags'])
            
            # Add time-specific emoji and greeting
            time_greetings = {
                'morning': '🌅 **Good Morning!**\n\n',
                'afternoon': '☀️ **Afternoon Inspiration**\n\n',
                'evening': '🌙 **Evening Reflection**\n\n',
                'night': '✨ **Late Night Wisdom**\n\n'
            }
            
            greeting = time_greetings.get(time_slot, '')
            final_content = greeting + post_content
            
            # Post to channel
            success = await self.bot.post_to_channel(final_content)
            
            if success:
                # Mark as posted
                self.quote_manager.mark_as_posted(quote_data['ID'], 'daily')
                logger.info(f"Successfully posted scheduled quote: {quote_data['ID']}")
            else:
                logger.error("Failed to post scheduled quote")
                
        except Exception as e:
            logger.error(f"Error creating scheduled post: {e}")
    
    async def _create_character_spotlight(self):
        """Create a character spotlight post"""
        try:
            logger.info("Creating character spotlight post")
            
            # Get popular characters
            popular_characters = self.quote_manager.get_popular_characters(20)
            
            if not popular_characters:
                logger.error("No character data available for spotlight")
                return
            
            # Select a random popular character
            import random
            character_name = random.choice(list(popular_characters.keys()))
            
            # Get quotes from this character
            character_quotes = self.quote_manager.quotes_df[
                self.quote_manager.quotes_df['Character'] == character_name
            ]
            
            if character_quotes.empty:
                logger.error(f"No quotes found for character: {character_name}")
                return
            
            # Get anime name
            anime_name = character_quotes.iloc[0]['Anime']
            quotes_list = character_quotes.head(3).to_dict('records')
            
            # Generate spotlight content
            spotlight_text = self.gemini_client.generate_character_spotlight(
                character_name, anime_name, quotes_list
            )
            
            # Format spotlight post
            post_content = f"🌟 **Character Spotlight** 🌟\n\n"
            post_content += f"**{character_name}** from *{anime_name}*\n\n"
            
            if spotlight_text:
                post_content += f"{spotlight_text}\n\n"
            
            # Add a featured quote
            featured_quote = quotes_list[0]
            post_content += f'💫 "{featured_quote["Quote"]}"\n\n'
            
            # Add hashtags
            hashtags = [
                "#CharacterSpotlight",
                f"#{anime_name.replace(' ', '').replace(':', '')}",
                f"#{character_name.replace(' ', '')}",
                "#AnimeCharacters",
                "#AnimeQuotes"
            ]
            post_content += ' '.join(hashtags)
            
            # Post to channel
            success = await self.bot.post_to_channel(post_content)
            
            if success:
                self.quote_manager.mark_as_posted(featured_quote['ID'], 'spotlight')
                logger.info(f"Successfully posted character spotlight: {character_name}")
            else:
                logger.error("Failed to post character spotlight")
                
        except Exception as e:
            logger.error(f"Error creating character spotlight: {e}")
    
    async def post_random_quote(self, category=None):
        """Post a random quote immediately"""
        try:
            logger.info(f"Posting random quote (category: {category})")
            
            if category:
                quote_data = self.quote_manager.get_quote_by_category(category)
            else:
                quote_data = self.quote_manager.get_random_quote()
            
            if not quote_data:
                logger.error("No quote data available")
                return False
            
            # Enhance with AI
            enhancement = self.gemini_client.enhance_post_content(quote_data, 'random')
            
            # Format post
            post_content = format_quote_post(quote_data, enhancement['hashtags'])
            
            # Post to channel
            success = await self.bot.post_to_channel(post_content)
            
            if success:
                self.quote_manager.mark_as_posted(quote_data['ID'], 'manual')
                logger.info(f"Successfully posted random quote: {quote_data['ID']}")
                return True
            else:
                logger.error("Failed to post random quote")
                return False
                
        except Exception as e:
            logger.error(f"Error posting random quote: {e}")
            return False
    
    async def start_scheduler(self):
        """Start the scheduler loop"""
        self.is_running = True
        logger.info("Starting post scheduler")
        
        while self.is_running:
            schedule.run_pending()
            await asyncio.sleep(60)  # Check every minute
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.is_running = False
        logger.info("Stopping post scheduler")
