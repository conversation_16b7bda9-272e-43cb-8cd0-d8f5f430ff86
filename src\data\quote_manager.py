import pandas as pd
import sqlite3
import logging
import random
from datetime import datetime, timedelta
from config.settings import settings

logger = logging.getLogger(__name__)

class QuoteManager:
    """Manages anime quotes data and database operations"""
    
    def __init__(self):
        self.csv_path = settings.CSV_PATH
        self.db_path = settings.DATABASE_PATH
        self.quotes_df = None
        self._init_database()
        self._load_quotes()
    
    def _init_database(self):
        """Initialize SQLite database for tracking posted quotes"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create table for posted quotes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS posted_quotes (
                    id INTEGER PRIMARY KEY,
                    quote_id INTEGER,
                    posted_at TIMESTAMP,
                    engagement_score INTEGER DEFAULT 0,
                    post_type TEXT DEFAULT 'daily'
                )
            ''')
            
            # Create table for analytics
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analytics (
                    id INTEGER PRIMARY KEY,
                    date DATE,
                    total_posts INTEGER,
                    avg_engagement REAL,
                    best_performing_quote_id INTEGER
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def _load_quotes(self):
        """Load quotes from CSV file"""
        try:
            self.quotes_df = pd.read_csv(self.csv_path)
            logger.info(f"Loaded {len(self.quotes_df)} quotes from CSV")
            
            # Clean and validate data
            self.quotes_df = self.quotes_df.dropna(subset=['Quote', 'Character', 'Anime'])
            self.quotes_df['Quote'] = self.quotes_df['Quote'].str.strip()
            
            logger.info(f"After cleaning: {len(self.quotes_df)} valid quotes")
            
        except Exception as e:
            logger.error(f"Error loading quotes: {e}")
            raise
    
    def get_random_quote(self, filters=None):
        """Get a random quote with optional filters"""
        try:
            available_quotes = self.quotes_df.copy()
            
            # Apply filters if provided
            if filters:
                if 'min_length' in filters:
                    available_quotes = available_quotes[
                        available_quotes['Quote_Length'] >= filters['min_length']
                    ]
                if 'max_length' in filters:
                    available_quotes = available_quotes[
                        available_quotes['Quote_Length'] <= filters['max_length']
                    ]
                if 'anime' in filters:
                    available_quotes = available_quotes[
                        available_quotes['Anime'].str.contains(filters['anime'], case=False, na=False)
                    ]
                if 'character' in filters:
                    available_quotes = available_quotes[
                        available_quotes['Character'].str.contains(filters['character'], case=False, na=False)
                    ]
            
            # Exclude recently posted quotes (last 30 days)
            recently_posted = self._get_recently_posted_quotes(days=30)
            if recently_posted:
                available_quotes = available_quotes[
                    ~available_quotes['ID'].isin(recently_posted)
                ]
            
            if available_quotes.empty:
                logger.warning("No available quotes found with current filters")
                # Fallback to any quote if no unposted quotes available
                available_quotes = self.quotes_df.copy()
            
            # Select random quote
            selected_quote = available_quotes.sample(n=1).iloc[0]
            return selected_quote.to_dict()
            
        except Exception as e:
            logger.error(f"Error getting random quote: {e}")
            return None
    
    def get_quote_by_category(self, category):
        """Get quotes by category (based on content analysis)"""
        category_keywords = {
            'motivation': ['strong', 'power', 'fight', 'never give up', 'believe', 'dream', 'achieve'],
            'love': ['love', 'heart', 'feelings', 'care', 'together', 'forever'],
            'friendship': ['friend', 'together', 'trust', 'bond', 'support'],
            'philosophy': ['life', 'death', 'truth', 'reality', 'world', 'meaning', 'exist'],
            'wisdom': ['learn', 'understand', 'know', 'wise', 'experience', 'mistake']
        }
        
        if category not in category_keywords:
            return self.get_random_quote()
        
        keywords = category_keywords[category]
        filtered_quotes = self.quotes_df[
            self.quotes_df['Quote'].str.contains('|'.join(keywords), case=False, na=False)
        ]
        
        if filtered_quotes.empty:
            return self.get_random_quote()
        
        return filtered_quotes.sample(n=1).iloc[0].to_dict()
    
    def mark_as_posted(self, quote_id, post_type='daily'):
        """Mark a quote as posted"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO posted_quotes (quote_id, posted_at, post_type)
                VALUES (?, ?, ?)
            ''', (quote_id, datetime.now(), post_type))
            
            conn.commit()
            conn.close()
            logger.info(f"Marked quote {quote_id} as posted")
            
        except Exception as e:
            logger.error(f"Error marking quote as posted: {e}")
    
    def _get_recently_posted_quotes(self, days=30):
        """Get list of recently posted quote IDs"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days)
            cursor.execute('''
                SELECT quote_id FROM posted_quotes 
                WHERE posted_at > ?
            ''', (cutoff_date,))
            
            result = [row[0] for row in cursor.fetchall()]
            conn.close()
            return result
            
        except Exception as e:
            logger.error(f"Error getting recently posted quotes: {e}")
            return []
    
    def get_popular_anime(self, limit=10):
        """Get most popular anime by quote count"""
        anime_counts = self.quotes_df['Anime'].value_counts().head(limit)
        return anime_counts.to_dict()
    
    def get_popular_characters(self, limit=10):
        """Get most popular characters by quote count"""
        character_counts = self.quotes_df['Character'].value_counts().head(limit)
        return character_counts.to_dict()
    
    def search_quotes(self, query, limit=5):
        """Search quotes by text content"""
        matching_quotes = self.quotes_df[
            self.quotes_df['Quote'].str.contains(query, case=False, na=False)
        ]
        return matching_quotes.head(limit).to_dict('records')
