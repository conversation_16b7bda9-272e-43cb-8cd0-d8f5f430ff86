import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load the dataset (replace 'your_dataset.csv' with your file path or URL)
df = pd.read_csv('data/lessreal-data.csv')

# 1. Basic Information
print("=== Dataset Info ===")
print(df.info())  # Data types, non-null counts, and memory usage
print("\n=== First 5 Rows ===")
print(df.head())  # Preview the data

# 2. Shape of the Dataset
print("\n=== Dataset Shape ===")
print(f"Rows: {df.shape[0]}, Columns: {df.shape[1]}")

# 3. Summary Statistics
print("\n=== Summary Statistics ===")
print(df.describe(include='all'))  # Includes numerical and categorical columns

# 4. Missing Values
print("\n=== Missing Values ===")
print(df.isnull().sum())  # Count of missing values per column

# 5. Unique Values per Column
print("\n=== Unique Values ===")
for column in df.columns:
    print(f"{column}: {df[column].nunique()} unique values")

# 6. Data Types
print("\n=== Data Types ===")
print(df.dtypes)

# 7. Correlation Matrix (for numerical columns)
print("\n=== Correlation Matrix ===")
numerical_cols = df.select_dtypes(include=['float64', 'int64']).columns
if len(numerical_cols) > 1:
    print(df[numerical_cols].corr())
else:
    print("Not enough numerical columns for correlation.")

# 8. Visualizations
# Set style for better visuals
sns.set_style("whitegrid")

# Histogram for numerical columns
for column in numerical_cols:
    plt.figure(figsize=(8, 5))
    sns.histplot(df[column], kde=True)
    plt.title(f'Distribution of {column}')
    plt.xlabel(column)
    plt.ylabel('Count')
    plt.show()

# Boxplot for numerical columns
for column in numerical_cols:
    plt.figure(figsize=(8, 5))
    sns.boxplot(x=df[column])
    plt.title(f'Boxplot of {column}')
    plt.show()

# Countplot for categorical columns
categorical_cols = df.select_dtypes(include=['object', 'category']).columns
for column in categorical_cols:
    plt.figure(figsize=(8, 5))
    sns.countplot(y=df[column], order=df[column].value_counts().index)
    plt.title(f'Count of {column}')
    plt.xlabel('Count')
    plt.ylabel(column)
    plt.show()

# 9. Optional: Save summary to a file
df.describe(include='all').to_csv('dataset_summary.csv')
print("\nSummary statistics saved to 'dataset_summary.csv'")

# 10. Optional: Check for duplicates
print("\n=== Duplicate Rows ===")
print(f"Number of duplicate rows: {df.duplicated().sum()}")